"use client";

import React, { useEffect, useState } from "react";
import CreatorEventCard from "./CreatorEventCard";
import CreatorScrollTab from "./CreatorScrollTab";
import { getAllTagsListData, getAllTagsWithCreatorEventCount } from "@/api/events";
import useInfiniteScrollCreator from "@/components/other/LoadMoreCreatorNew";
import {
  filterTagsByLanguage,
  filterTagsWithEventCount,
  getCurrentTimestamp,
  removeDuplicatesByTagSlug,
} from "@/utils";
import { Spinner } from "@heroui/react";
import { useTranslation } from "react-i18next";
import { useGlobalState } from "~~/services/store/store";

const CreatorEventCardList: React.FC = () => {
  const { current_language } = useGlobalState().nativeCurrency;
  const timeStamp = getCurrentTimestamp();
  const { t } = useTranslation();

  const [tagListData, setTagListData] = useState<any[]>([]);
  const [selectedTagId, setSelectedTagId] = useState<string>("all");
  const [eventParams, setEventParams] = useState({
    tag_id: "all",
    limit: 16,
    offset: 0,
  });

  const [availableTagIds, setAvailableTagIds] = useState<string[]>([]);
  const {
    data: eventCardListData,
    hasMore,
    loadMoreRef,
    setQueryParams,
  } = useInfiniteScrollCreator(eventParams, availableTagIds);

  // 处理标签切换
  const handleTagClick = (tagId: string) => {
    setSelectedTagId(tagId);
    setEventParams(prev => ({
      ...prev,
      tag_id: tagId,
      offset: 0,
    }));
    setQueryParams({
      tag_id: tagId,
      limit: 16,
      offset: 0,
    });
  };

  // 获取标签数据
  useEffect(() => {
    if (current_language === undefined) {
      return;
    }

    const allTag = {
      id: "all",
      label: t("Markets_Status_All") || "All",
      slug: "all",
      region: current_language,
    };

    // 先获取所有标签，找到creator标签的ID
    getAllTagsListData()
      .then((allTagsRes: any) => {
        const allTags = allTagsRes.data.tags || [];

        const creatorTag = allTags.find((tag: any) => tag.slug?.toLowerCase() === "creator");

        if (!creatorTag) {
          console.error(
            "❌ CreatorEventCardList: Creator tag not found in tags:",
            allTags.map((tag: any) => tag.slug),
          );
          return;
        }

        // 使用找到的creator标签ID调用专门的接口
        return getAllTagsWithCreatorEventCount({
          creatorTagId: creatorTag.id,
          active: true,
          closed: false,
        });
      })
      .then((res: any) => {
        if (!res) {
          return;
        }

        const uniqueItems = removeDuplicatesByTagSlug(res.data.tags);

        const filteredByLanguage = filterTagsByLanguage(current_language, uniqueItems);

        // 同时过滤掉没有事件的空标签
        const filteredTags = filterTagsWithEventCount(filteredByLanguage, {
          filterEmpty: true,
          filterCreator: true,
          includeAdvancedFilters: false,
        });

        const finalTagList = [allTag, ...filteredTags];
        setTagListData(finalTagList);

        // 收集当前语言下可用的标签ID
        const tagIds = filteredTags.map((tag: any) => tag.id.toString());
        setAvailableTagIds(tagIds);

        // 切换语言区后，重置标签选择为"All"
        setSelectedTagId("all");
        setEventParams(prev => ({
          ...prev,
          tag_id: "all",
          offset: 0,
        }));
        // 注意：不在这里调用setQueryParams，让useInfiniteScrollCreator的语言变化逻辑来处理
      })
      .catch(error => {
        console.error("❌ CreatorEventCardList: Error fetching creator tags:", error);
      });
  }, [current_language, t, setQueryParams]);

  return (
    <div className="w-full flex flex-col items-center justify-center mb-12">
      <CreatorScrollTab tagListData={tagListData} selectedTagId={selectedTagId} onTagClick={handleTagClick} />

      {eventCardListData.length > 0 ? (
        <div className="w-full gap-3 grid" style={{ gridTemplateColumns: "repeat(auto-fill, minmax(300px, 1fr))" }}>
          {eventCardListData.map((data, index) => (
            <CreatorEventCard
              key={`event-${data.id}-${index}-${eventParams.offset}`}
              eventCardData={data}
              timeStamp={timeStamp}
            />
          ))}
        </div>
      ) : (
        !hasMore && (
          <div className="flex flex-col items-center justify-center py-16 text-center">
            <div className="text-gray-400 text-md mb-2">{t("Portfolio_No_More_Data") || "No more data"}</div>
          </div>
        )
      )}

      {/* 无限滚动加载更多触发器 */}
      <div ref={loadMoreRef} className="flex-center w-full h-16 mt-6">
        {hasMore ? (
          <div className="flex items-center gap-2 text-gray-500">
            <Spinner size="sm" color="default" />
            <span className="text-sm">{t("sports.loading_events") || "Loading events..."}</span>
          </div>
        ) : (
          eventCardListData.length > 0 && (
            <div className="text-gray-400 text-sm">{t("Portfolio_No_More_Data") || "No more data"}</div>
          )
        )}
      </div>
    </div>
  );
};

export default CreatorEventCardList;
