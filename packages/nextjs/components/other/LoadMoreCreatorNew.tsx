"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getAllHomeEvents, getHomeEventsByTag } from "@/api/events";
import { filterEventsByLanguage } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string | number | null;
}

const useInfiniteScrollCreator = (initialParams: any, availableTagIds?: string[]) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  const loadMore = useCallback(() => {
    if (isLoading) {
      return;
    }
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [isLoading]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  // 监听语言变化，重置数据
  useEffect(() => {
    setData([]);
    setHasMore(true);
    setInitialLoad(true);
    // 重置查询参数的offset
    setQueryParams(prev => ({
      ...prev,
      offset: 0,
    }));
  }, [current_language]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      if (isLoading) {
        return;
      }

      setIsLoading(true);
      try {
        // 根据tag_id选择不同的API
        const apiParams = {
          limit: queryParams.limit,
          offset: queryParams.offset,
          active: true,
          closed: false,
        };

        let res;
        if (queryParams.tag_id === "all" || !queryParams.tag_id) {
          res = await getAllHomeEvents(apiParams);
        } else {
          res = await getHomeEventsByTag({
            ...apiParams,
            tag_id: Number(queryParams.tag_id),
          });
        }
        const allEvents = res.data.events || [];
        const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);
        let finalEvents = filteredByLanguage;

        // Creator事件过滤逻辑 - 所有事件都必须包含Creator标签
        finalEvents = filteredByLanguage.filter((event: any) => {
          if (!event?.event_tags || !Array.isArray(event.event_tags)) {
            return false;
          }

          // 首先检查是否包含Creator标签
          const hasCreatorTag = event.event_tags.some((eventTag: any) => {
            const tagSlug = eventTag?.tag?.slug?.toLowerCase();
            return tagSlug === "creator";
          });

          // 如果没有Creator标签，直接过滤掉
          if (!hasCreatorTag) {
            return false;
          }

          // 如果是All模式，还需要检查是否包含当前地区的可用标签
          if (queryParams.tag_id === "all") {
            if (!availableTagIds || availableTagIds.length === 0) {
              return true; // 如果没有可用标签限制，显示所有Creator事件
            }

            // 检查是否包含当前地区的任一可用标签
            return event.event_tags.some((eventTag: any) => {
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              return tagId && availableTagIds.includes(tagId);
            });
          } else {
            // 特定标签模式：检查是否包含指定标签
            return event.event_tags.some((eventTag: any) => {
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              const queryTagId = queryParams.tag_id?.toString();
              return tagId === queryTagId;
            });
          }
        });

        const filteredCreatorEvents = finalEvents;

        if (queryParams.offset === 0) {
          setData(filteredCreatorEvents);
          setHasMore(true);
        } else {
          setData(prevData => [...prevData, ...filteredCreatorEvents]);
        }

        // 基于原始API数据判断是否还有更多，而不是过滤后的数据
        if (allEvents.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("❌ useInfiniteScrollCreator: Error fetching creator events:", error);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 确保有语言信息时才请求数据
    if (current_language) {
      if (queryParams.tag_id === "all") {
        // "all"模式需要等待availableTagIds准备好
        if (availableTagIds !== undefined) {
          fetchDataAsync();
        }
      } else {
        // 非"all"模式可以直接加载，不需要等待availableTagIds
        fetchDataAsync();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, current_language]); // 移除availableTagIds依赖，避免影响非all模式的切换

  // 单独处理availableTagIds变化，只影响all模式
  useEffect(() => {
    if (current_language && queryParams.tag_id === "all" && availableTagIds !== undefined) {
      const fetchDataAsync = async () => {
        if (isLoading) {
          return;
        }

        setIsLoading(true);
        try {
          const apiParams = {
            limit: queryParams.limit,
            offset: queryParams.offset,
            active: true,
            closed: false,
          };

          const res = await getAllHomeEvents(apiParams);
          const allEvents = res.data.events || [];
          const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);

          // Creator事件过滤逻辑 - 所有事件都必须包含Creator标签
          const finalEvents = filteredByLanguage.filter((event: any) => {
            if (!event?.event_tags || !Array.isArray(event.event_tags)) {
              return false;
            }

            // 首先检查是否包含Creator标签
            const hasCreatorTag = event.event_tags.some((eventTag: any) => {
              const tagSlug = eventTag?.tag?.slug?.toLowerCase();
              return tagSlug === "creator";
            });

            // 如果没有Creator标签，直接过滤掉
            if (!hasCreatorTag) {
              return false;
            }

            // All模式：检查是否包含当前地区的可用标签
            if (!availableTagIds || availableTagIds.length === 0) {
              return true; // 如果没有可用标签限制，显示所有Creator事件
            }

            // 检查是否包含当前地区的任一可用标签
            return event.event_tags.some((eventTag: any) => {
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              return tagId && availableTagIds.includes(tagId);
            });
          });

          if (queryParams.offset === 0) {
            setData(finalEvents);
            setHasMore(true);
          } else {
            setData(prevData => [...prevData, ...finalEvents]);
          }

          // 基于原始API数据判断是否还有更多
          if (allEvents.length < queryParams.limit) {
            setHasMore(false);
          }
          setInitialLoad(false);
        } catch (error) {
          console.error("❌ useInfiniteScrollCreator: Error fetching creator events for all mode:", error);
          setHasMore(false);
        } finally {
          setIsLoading(false);
        }
      };

      fetchDataAsync();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [availableTagIds]); // 只依赖availableTagIds，专门处理all模式的数据更新

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  return {
    data,
    hasMore,
    isLoading,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  };
};

export default useInfiniteScrollCreator;
