"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getAllHomeEvents, getHomeEventsByTag } from "@/api/events";
import { filterEventsByLanguage } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string | number | null;
}

const useInfiniteScrollCreator = (initialParams: any, availableTagIds?: string[]) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  const loadMore = useCallback(() => {
    if (isLoading) {
      return;
    }
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [isLoading]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  // 监听语言变化，重置数据
  useEffect(() => {
    setData([]);
    setHasMore(true);
    setInitialLoad(true);
    // 重置查询参数的offset，但保持tag_id为"all"
    setQueryParams(prev => ({
      ...prev,
      tag_id: "all", // 确保切换语言后默认为all
      offset: 0,
    }));
  }, [current_language]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      if (isLoading) {
        return;
      }

      setIsLoading(true);
      try {
        // 根据tag_id选择不同的API
        const apiParams = {
          limit: queryParams.limit,
          offset: queryParams.offset,
          active: true,
          closed: false,
        };

        let res;
        if (queryParams.tag_id === "all" || !queryParams.tag_id) {
          res = await getAllHomeEvents(apiParams);
        } else {
          res = await getHomeEventsByTag({
            ...apiParams,
            tag_id: Number(queryParams.tag_id),
          });
        }
        const allEvents = res.data.events || [];
        const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);
        let finalEvents = filteredByLanguage;

        // Creator事件过滤逻辑 - 所有事件都必须包含Creator标签
        finalEvents = filteredByLanguage.filter((event: any) => {
          if (!event?.event_tags || !Array.isArray(event.event_tags)) {
            return false;
          }

          // 首先检查是否包含Creator标签
          const hasCreatorTag = event.event_tags.some((eventTag: any) => {
            const tagSlug = eventTag?.tag?.slug?.toLowerCase();
            return tagSlug === "creator";
          });

          // 如果没有Creator标签，直接过滤掉
          if (!hasCreatorTag) {
            return false;
          }

          // 如果是All模式，还需要检查是否包含当前地区的可用标签
          if (queryParams.tag_id === "all") {
            if (!availableTagIds || availableTagIds.length === 0) {
              return true; // 如果没有可用标签限制，显示所有Creator事件
            }

            // 检查是否包含当前地区的任一可用标签
            return event.event_tags.some((eventTag: any) => {
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              return tagId && availableTagIds.includes(tagId);
            });
          } else {
            // 特定标签模式：检查是否包含指定标签
            return event.event_tags.some((eventTag: any) => {
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              const queryTagId = queryParams.tag_id?.toString();
              return tagId === queryTagId;
            });
          }
        });

        const filteredCreatorEvents = finalEvents;

        if (queryParams.offset === 0) {
          setData(filteredCreatorEvents);
          setHasMore(true);
        } else {
          setData(prevData => [...prevData, ...filteredCreatorEvents]);
        }

        // 基于原始API数据判断是否还有更多，而不是过滤后的数据
        if (allEvents.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("❌ useInfiniteScrollCreator: Error fetching creator events:", error);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 确保有语言信息时才请求数据
    if (current_language) {
      if (queryParams.tag_id === "all") {
        // "all"模式需要等待availableTagIds准备好
        if (availableTagIds !== undefined) {
          fetchDataAsync();
        }
      } else {
        // 非"all"模式可以直接加载
        fetchDataAsync();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, current_language]); // 移除availableTagIds依赖，避免影响非all模式的切换

  // 单独处理availableTagIds变化，只在all模式且offset为0时重新加载
  useEffect(() => {
    if (
      current_language &&
      queryParams.tag_id === "all" &&
      availableTagIds !== undefined &&
      queryParams.offset === 0 &&
      !isLoading // 避免在已经加载中时重复触发
    ) {
      // 使用setTimeout避免与其他状态更新冲突
      const timer = setTimeout(() => {
        setQueryParams(prev => ({ ...prev, offset: 0 }));
      }, 50);

      return () => clearTimeout(timer);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [availableTagIds]); // 只依赖availableTagIds

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  return {
    data,
    hasMore,
    isLoading,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  };
};

export default useInfiniteScrollCreator;
