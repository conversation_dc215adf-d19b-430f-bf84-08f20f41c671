"use client";

import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { getAllHomeEvents, getHomeEventsByTag } from "@/api/events";
import { filterEventsByLanguage } from "@/utils";
import { useGlobalState } from "~~/services/store/store";

interface QueryParams {
  limit: number;
  offset: number;
  tag_id: string | number | null;
}

const useInfiniteScrollCreator = (initialParams: any, availableTagIds?: string[]) => {
  const [data, setData] = useState<any[]>([]);
  const [queryParams, setQueryParams] = useState<QueryParams>(initialParams);
  const [hasMore, setHasMore] = useState(true);
  const [isLoading, setIsLoading] = useState(false);
  const loadMoreRef = useRef<HTMLDivElement | null>(null);
  const [initialLoad, setInitialLoad] = useState(true);
  const { current_language } = useGlobalState().nativeCurrency;

  const loadMore = useCallback(() => {
    if (isLoading) {
      return;
    }
    setQueryParams(prevParams => ({
      ...prevParams,
      offset: prevParams.offset + prevParams.limit,
    }));
  }, [isLoading]);

  const observer = useMemo(() => {
    if (typeof window === "undefined") return null;
    return new IntersectionObserver(
      entries => {
        if (entries[0].isIntersecting && hasMore && !initialLoad) {
          loadMore();
        }
      },
      {
        root: null,
        rootMargin: "20px",
        threshold: 1.0,
      },
    );
  }, [hasMore, loadMore, initialLoad]);

  // 监听语言变化，重置数据
  useEffect(() => {
    setData([]);
    setHasMore(true);
    setInitialLoad(true);
    // 重置查询参数的offset
    setQueryParams(prev => ({
      ...prev,
      offset: 0,
    }));
  }, [current_language]);

  useEffect(() => {
    const fetchDataAsync = async () => {
      if (isLoading) {
        return;
      }

      setIsLoading(true);
      try {
        // 根据tag_id选择不同的API
        const apiParams = {
          limit: queryParams.limit,
          offset: queryParams.offset,
          active: true,
          closed: false,
        };

        let res;
        if (queryParams.tag_id === "all" || !queryParams.tag_id) {
          res = await getAllHomeEvents(apiParams);
        } else {
          res = await getHomeEventsByTag({
            ...apiParams,
            tag_id: Number(queryParams.tag_id),
          });
        }
        const allEvents = res.data.events || [];
        const filteredByLanguage = filterEventsByLanguage(current_language, allEvents);
        let finalEvents = filteredByLanguage;

        // Creator事件过滤逻辑
        if (queryParams.tag_id === "all") {
          // All标签模式：显示所有包含Creator标签的事件
          finalEvents = filteredByLanguage.filter((event: any) => {
            if (!event?.event_tags || !Array.isArray(event.event_tags)) {
              return false;
            }

            // 检查是否包含Creator标签
            return event.event_tags.some((eventTag: any) => {
              const tagSlug = eventTag?.tag?.slug?.toLowerCase();
              return tagSlug === "creator";
            });
          });
        } else {
          // 特定标签模式：显示同时包含Creator标签和该特定标签的事件
          finalEvents = filteredByLanguage.filter((event: any) => {
            if (!event?.event_tags || !Array.isArray(event.event_tags)) {
              return false;
            }

            let hasCreatorTag = false;
            let hasSpecificTag = false;

            event.event_tags.forEach((eventTag: any) => {
              const tagSlug = eventTag?.tag?.slug?.toLowerCase();
              const tagId = eventTag?.tag?.id?.toString() || eventTag?.tag_id?.toString();
              const queryTagId = queryParams.tag_id?.toString();

              if (tagSlug === "creator") {
                hasCreatorTag = true;
              }
              if (tagId === queryTagId) {
                hasSpecificTag = true;
              }
            });

            // 必须同时包含Creator标签和指定标签
            return hasCreatorTag && hasSpecificTag;
          });
        }

        const filteredCreatorEvents = finalEvents;

        if (queryParams.offset === 0) {
          setData(filteredCreatorEvents);
          setHasMore(true);
        } else {
          setData(prevData => [...prevData, ...filteredCreatorEvents]);
        }

        // 基于原始API数据判断是否还有更多，而不是过滤后的数据
        if (allEvents.length < queryParams.limit) {
          setHasMore(false);
        }
        setInitialLoad(false);
      } catch (error) {
        console.error("❌ useInfiniteScrollCreator: Error fetching creator events:", error);
        setHasMore(false);
      } finally {
        setIsLoading(false);
      }
    };

    // 确保有语言信息时才请求数据
    if (current_language) {
      fetchDataAsync();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [queryParams, current_language]); // 移除availableTagIds依赖，因为现在直接基于Creator标签过滤

  useEffect(() => {
    if (!observer || !loadMoreRef.current) return;

    observer.observe(loadMoreRef.current);
    return () => observer.disconnect();
  }, [observer]);

  return {
    data,
    hasMore,
    isLoading,
    loadMoreRef,
    setQueryParams,
    setHasMore,
  };
};

export default useInfiniteScrollCreator;
